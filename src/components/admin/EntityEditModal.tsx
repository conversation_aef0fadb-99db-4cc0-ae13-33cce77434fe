"use client";

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Save, X } from 'lucide-react';
import { Entity, UpdateEntityDto, EntityType, Category, Tag, Feature } from '@/types/entity';
import { adminUpdateEntity, getEntityTypes, getCategories, getTags, getFeatures, getEntityById } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';

// Validation schema for entity updates - comprehensive schema matching API
const updateEntitySchema = z.object({
  // Core entity information
  name: z.string().min(1, 'Name is required').optional(),
  short_description: z.string().optional(),
  description: z.string().optional(),
  website_url: z.string().url('Invalid URL').optional(),
  logo_url: z.string().url('Invalid URL').optional().or(z.literal('')),
  documentation_url: z.string().url('Invalid URL').optional().or(z.literal('')),
  contact_url: z.string().optional().or(z.literal('')),
  privacy_policy_url: z.string().url('Invalid URL').optional().or(z.literal('')),
  founded_year: z.number().min(1800).max(new Date().getFullYear()).optional().or(z.literal(null)),

  // Entity type and categorization
  entity_type_id: z.string().optional(),

  // SEO metadata
  meta_title: z.string().optional(),
  meta_description: z.string().optional(),

  // Company/organization details
  employee_count_range: z.enum(['C1_10', 'C11_50', 'C51_200', 'C201_500', 'C501_1000', 'C1001_5000', 'C5001_PLUS']).optional(),
  funding_stage: z.enum(['SEED', 'PRE_SEED', 'SERIES_A', 'SERIES_B', 'SERIES_C', 'SERIES_D_PLUS', 'PUBLIC']).optional(),
  location_summary: z.string().optional(),

  // Referral and affiliate
  ref_link: z.string().url('Invalid URL').optional().or(z.literal('')),
  affiliate_status: z.enum(['NONE', 'APPLIED', 'APPROVED', 'REJECTED']).optional(),

  // Review and sentiment data
  scraped_review_sentiment_label: z.string().optional(),
  scraped_review_sentiment_score: z.number().min(0).max(1).optional(),
  scraped_review_count: z.number().min(0).optional(),

  // Status and features
  status: z.enum(['PENDING', 'ACTIVE', 'REJECTED', 'INACTIVE', 'ARCHIVED', 'NEEDS_REVISION']).optional(),
  has_live_chat: z.boolean().optional(),

  // Social links
  social_links: z.object({
    twitter: z.string().optional(),
    linkedin: z.string().optional(),
    facebook: z.string().optional(),
    instagram: z.string().optional(),
    youtube: z.string().optional(),
    github: z.string().optional(),
    discord: z.string().optional(),
    slack: z.string().optional(),
  }).optional(),

  // Entity-specific details (basic support for now)
  tool_details: z.object({
    technical_level: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
    has_free_tier: z.boolean().optional(),
    has_api: z.boolean().optional(),
    api_documentation_url: z.string().url().optional().or(z.literal('')),
    pricing_model: z.string().optional(),
    price_range: z.enum(['FREE', 'LOW', 'MEDIUM', 'HIGH', 'ENTERPRISE']).optional(),
    pricing_details: z.string().optional(),
    pricing_url: z.string().url().optional().or(z.literal('')),
    learning_curve: z.string().optional(),
    key_features: z.array(z.string()).optional(),
    use_cases: z.array(z.string()).optional(),
    integrations: z.array(z.string()).optional(),
    frameworks: z.array(z.string()).optional(),
    libraries: z.array(z.string()).optional(),
    programming_languages: z.array(z.string()).optional(),
    platforms: z.array(z.string()).optional(),
    target_audience: z.array(z.string()).optional(),
    deployment_options: z.array(z.string()).optional(),
    supported_os: z.array(z.string()).optional(),
    support_channels: z.array(z.string()).optional(),
    mobile_support: z.boolean().optional(),
    api_access: z.boolean().optional(),
    customization_level: z.string().optional(),
    trial_available: z.boolean().optional(),
    demo_available: z.boolean().optional(),
    open_source: z.boolean().optional(),
    support_email: z.string().email().optional().or(z.literal('')),
    community_url: z.string().url().optional().or(z.literal('')),
  }).optional(),

  course_details: z.object({
    instructor_name: z.string().optional(),
    duration_text: z.string().optional(),
    skill_level: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
    prerequisites: z.array(z.string()).optional(),
    learning_outcomes: z.array(z.string()).optional(),
    language: z.string().optional(),
    syllabus_url: z.string().url().optional().or(z.literal('')),
    enrollment_count: z.number().min(0).optional(),
    certificate_available: z.boolean().optional(),
  }).optional(),

  job_details: z.object({
    company_name: z.string().optional(),
    employment_types: z.array(z.enum(['FULL_TIME', 'PART_TIME', 'CONTRACT', 'FREELANCE', 'INTERNSHIP', 'TEMPORARY'])).optional(),
    experience_level: z.string().optional(),
    location_types: z.array(z.string()).optional(),
    salary_min: z.number().min(0).optional(),
    salary_max: z.number().min(0).optional(),
    application_url: z.string().url().optional().or(z.literal('')),
    job_description: z.string().optional(),
    is_remote: z.boolean().optional(),
    location: z.string().optional(),
    job_type: z.string().optional(),
    key_responsibilities: z.array(z.string()).optional(),
    required_skills: z.array(z.string()).optional(),
    benefits: z.array(z.string()).optional(),
    remote_policy: z.string().optional(),
    visa_sponsorship: z.boolean().optional(),
  }).optional(),
});

type UpdateEntityFormData = z.infer<typeof updateEntitySchema>;

interface EntityEditModalProps {
  entity: Entity | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (updatedEntity: Entity) => void;
}

export const EntityEditModal: React.FC<EntityEditModalProps> = ({
  entity,
  isOpen,
  onClose,
  onSuccess,
}) => {
  const { session } = useAuth();
  const [loading, setLoading] = useState(false);
  const [fetchingEntity, setFetchingEntity] = useState(false);
  const [completeEntity, setCompleteEntity] = useState<Entity | null>(null);

  // State for reference data
  const [entityTypes, setEntityTypes] = useState<EntityType[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [features, setFeatures] = useState<Feature[]>([]);

  // State for selected items
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<UpdateEntityFormData>({
    resolver: zodResolver(updateEntitySchema),
  });

  // Fetch complete entity data when modal opens
  useEffect(() => {
    const fetchCompleteEntity = async () => {
      if (!entity?.id || !isOpen || !session?.access_token) return;

      try {
        setFetchingEntity(true);
        console.log('Fetching complete entity data for ID:', entity.id);

        const fullEntity = await getEntityById(entity.id, session.access_token);
        console.log('Complete entity data received:', fullEntity);

        setCompleteEntity(fullEntity);
      } catch (error) {
        console.error('Error fetching complete entity data:', error);
        // Fallback to the entity passed as prop
        setCompleteEntity(entity);
      } finally {
        setFetchingEntity(false);
      }
    };

    fetchCompleteEntity();
  }, [entity?.id, isOpen, session?.access_token]);

  // Load reference data
  useEffect(() => {
    const loadReferenceData = async () => {
      try {
        const [entityTypesData, categoriesData, tagsData, featuresData] = await Promise.all([
          getEntityTypes().catch(err => {
            console.error('Error loading entity types:', err);
            return [];
          }),
          getCategories().catch(err => {
            console.error('Error loading categories:', err);
            return [];
          }),
          getTags().catch(err => {
            console.error('Error loading tags:', err);
            return [];
          }),
          getFeatures().catch(err => {
            console.error('Error loading features:', err);
            return [];
          }),
        ]);
        setEntityTypes(entityTypesData);
        setCategories(categoriesData);
        setTags(tagsData);
        setFeatures(featuresData);
      } catch (error) {
        console.error('Error loading reference data:', error);
        // Don't throw error, just log it and continue with empty arrays
      }
    };

    if (isOpen) {
      loadReferenceData();
    }
  }, [isOpen]);

  // Populate form when complete entity data is available
  useEffect(() => {
    if (completeEntity && isOpen && !fetchingEntity) {
      console.log('Populating form with complete entity data:', completeEntity);

      // Reset form with complete entity data - using proper field mapping
      const formData = {
        // Core fields
        name: completeEntity.name || '',
        short_description: completeEntity.shortDescription || '',
        description: completeEntity.description || '',
        website_url: completeEntity.websiteUrl || '',
        logo_url: completeEntity.logoUrl || '',
        documentation_url: completeEntity.documentationUrl || '',
        contact_url: completeEntity.contactUrl || '',
        privacy_policy_url: completeEntity.privacyPolicyUrl || '',
        founded_year: completeEntity.foundedYear || undefined,

        // Entity type and categorization
        entity_type_id: completeEntity.entityType?.id || '',

        // SEO metadata
        meta_title: completeEntity.metaTitle || '',
        meta_description: completeEntity.metaDescription || '',

        // Company/organization details - check multiple possible field names
        employee_count_range: (completeEntity as any).employeeCountRange || (completeEntity as any).employee_count_range || '',
        funding_stage: (completeEntity as any).fundingStage || (completeEntity as any).funding_stage || '',
        location_summary: (completeEntity as any).locationSummary || (completeEntity as any).location_summary || '',

        // Referral and affiliate
        ref_link: (completeEntity as any).refLink || (completeEntity as any).ref_link || '',
        affiliate_status: (completeEntity as any).affiliateStatus || (completeEntity as any).affiliate_status || 'NONE',

        // Review and sentiment data
        scraped_review_sentiment_label: (completeEntity as any).scrapedReviewSentimentLabel || (completeEntity as any).scraped_review_sentiment_label || '',
        scraped_review_sentiment_score: (completeEntity as any).scrapedReviewSentimentScore || (completeEntity as any).scraped_review_sentiment_score || undefined,
        scraped_review_count: (completeEntity as any).scrapedReviewCount || (completeEntity as any).scraped_review_count || undefined,

        // Status and features
        status: completeEntity.status as any,
        has_live_chat: (completeEntity as any).hasLiveChat || (completeEntity as any).has_live_chat || false,

        // Social links - handle both camelCase and snake_case
        social_links: {
          twitter: completeEntity.socialLinks?.twitter || (completeEntity as any).social_links?.twitter || '',
          linkedin: completeEntity.socialLinks?.linkedin || (completeEntity as any).social_links?.linkedin || '',
          facebook: completeEntity.socialLinks?.facebook || (completeEntity as any).social_links?.facebook || '',
          instagram: completeEntity.socialLinks?.instagram || (completeEntity as any).social_links?.instagram || '',
          youtube: completeEntity.socialLinks?.youtube || (completeEntity as any).social_links?.youtube || '',
          github: completeEntity.socialLinks?.github || (completeEntity as any).social_links?.github || '',
          discord: completeEntity.socialLinks?.discord || (completeEntity as any).social_links?.discord || '',
          slack: completeEntity.socialLinks?.slack || (completeEntity as any).social_links?.slack || '',
        },

        // Entity-specific details
        tool_details: (completeEntity as any).toolDetails || (completeEntity as any).tool_details || {},
        course_details: (completeEntity as any).courseDetails || (completeEntity as any).course_details || {},
        job_details: (completeEntity as any).jobDetails || (completeEntity as any).job_details || {},
      };

      console.log('Form data being set:', formData);
      reset(formData);

      // Set selected arrays with safe null checks
      setSelectedCategories(Array.isArray(completeEntity.categories) ? completeEntity.categories.map(c => c.id) : []);
      setSelectedTags(Array.isArray(completeEntity.tags) ? completeEntity.tags.map(t => t.id) : []);
      setSelectedFeatures(Array.isArray(completeEntity.features) ? completeEntity.features.map(f => f.id) : []);
    }
  }, [completeEntity, isOpen, fetchingEntity, reset]);

  const onSubmit = async (data: UpdateEntityFormData) => {
    if (!entity || !session?.access_token) return;

    try {
      setLoading(true);

      // Prepare update payload
      const updatePayload: UpdateEntityDto = {
        ...data,
        category_ids: selectedCategories,
        tag_ids: selectedTags,
        feature_ids: selectedFeatures,
      };

      // Remove empty strings and convert to undefined, handle null values
      Object.keys(updatePayload).forEach(key => {
        const value = (updatePayload as any)[key];
        if (value === '' || value === null) {
          (updatePayload as any)[key] = undefined;
        }
      });

      const updatedEntity = await adminUpdateEntity(entity.id, updatePayload, session.access_token);
      onSuccess(updatedEntity);
      onClose();
    } catch (error: any) {
      console.error('Error updating entity:', error);
      const errorMessage = error?.message || 'Failed to update entity. Please try again.';
      alert(`Update failed: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting && !loading) {
      onClose();
    }
  };

  if (!entity) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Entity: {entity.name}</DialogTitle>
        </DialogHeader>

        {fetchingEntity && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading complete entity data...</span>
          </div>
        )}

        {!fetchingEntity && completeEntity && (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="categorization">Categories & Tags</TabsTrigger>
              <TabsTrigger value="metadata">Metadata & SEO</TabsTrigger>
              <TabsTrigger value="social">Social & Reviews</TabsTrigger>
              <TabsTrigger value="details">Entity Details</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    {...register('name')}
                    placeholder="Entity name"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="website_url">Website URL *</Label>
                  <Input
                    id="website_url"
                    {...register('website_url')}
                    placeholder="https://example.com"
                  />
                  {errors.website_url && (
                    <p className="text-sm text-red-600 mt-1">{errors.website_url.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="logo_url">Logo URL</Label>
                  <Input
                    id="logo_url"
                    {...register('logo_url')}
                    placeholder="https://example.com/logo.png"
                  />
                  {errors.logo_url && (
                    <p className="text-sm text-red-600 mt-1">{errors.logo_url.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="founded_year">Founded Year</Label>
                  <Input
                    id="founded_year"
                    type="number"
                    {...register('founded_year', { valueAsNumber: true })}
                    placeholder="2023"
                  />
                  {errors.founded_year && (
                    <p className="text-sm text-red-600 mt-1">{errors.founded_year.message}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="short_description">Short Description</Label>
                <Textarea
                  id="short_description"
                  {...register('short_description')}
                  placeholder="Brief description..."
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="description">Full Description</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Detailed description..."
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="documentation_url">Documentation URL</Label>
                  <Input
                    id="documentation_url"
                    {...register('documentation_url')}
                    placeholder="https://docs.example.com"
                  />
                </div>

                <div>
                  <Label htmlFor="contact_url">Contact URL</Label>
                  <Input
                    id="contact_url"
                    {...register('contact_url')}
                    placeholder="https://example.com/contact"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="categorization" className="space-y-4">
              <div>
                <Label htmlFor="entity_type_id">Entity Type</Label>
                <Select
                  value={watch('entity_type_id')}
                  onValueChange={(value) => setValue('entity_type_id', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select entity type" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.isArray(entityTypes) && entityTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Categories</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2 max-h-40 overflow-y-auto border rounded p-2">
                  {Array.isArray(categories) && categories.map((category) => (
                    <label key={category.id} className="flex items-center space-x-2 text-sm">
                      <input
                        type="checkbox"
                        checked={selectedCategories.includes(category.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedCategories([...selectedCategories, category.id]);
                          } else {
                            setSelectedCategories(selectedCategories.filter(id => id !== category.id));
                          }
                        }}
                        className="rounded"
                      />
                      <span>{category.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <Label>Tags</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2 max-h-40 overflow-y-auto border rounded p-2">
                  {Array.isArray(tags) && tags.map((tag) => (
                    <label key={tag.id} className="flex items-center space-x-2 text-sm">
                      <input
                        type="checkbox"
                        checked={selectedTags.includes(tag.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedTags([...selectedTags, tag.id]);
                          } else {
                            setSelectedTags(selectedTags.filter(id => id !== tag.id));
                          }
                        }}
                        className="rounded"
                      />
                      <span>{tag.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <Label>Features</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2 max-h-40 overflow-y-auto border rounded p-2">
                  {Array.isArray(features) && features.map((feature) => (
                    <label key={feature.id} className="flex items-center space-x-2 text-sm">
                      <input
                        type="checkbox"
                        checked={selectedFeatures.includes(feature.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedFeatures([...selectedFeatures, feature.id]);
                          } else {
                            setSelectedFeatures(selectedFeatures.filter(id => id !== feature.id));
                          }
                        }}
                        className="rounded"
                      />
                      <span>{feature.name}</span>
                    </label>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="metadata" className="space-y-4">
              <div>
                <Label htmlFor="meta_title">Meta Title (SEO)</Label>
                <Input
                  id="meta_title"
                  {...register('meta_title')}
                  placeholder="SEO title for search engines"
                />
              </div>

              <div>
                <Label htmlFor="meta_description">Meta Description (SEO)</Label>
                <Textarea
                  id="meta_description"
                  {...register('meta_description')}
                  placeholder="SEO description for search engines"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="employee_count_range">Employee Count Range</Label>
                  <Select
                    value={watch('employee_count_range')}
                    onValueChange={(value) => setValue('employee_count_range', value as 'C1_10' | 'C11_50' | 'C51_200' | 'C201_500' | 'C501_1000' | 'C1001_5000' | 'C5001_PLUS')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="C1_10">1-10</SelectItem>
                      <SelectItem value="C11_50">11-50</SelectItem>
                      <SelectItem value="C51_200">51-200</SelectItem>
                      <SelectItem value="C201_500">201-500</SelectItem>
                      <SelectItem value="C501_1000">501-1000</SelectItem>
                      <SelectItem value="C1001_5000">1001-5000</SelectItem>
                      <SelectItem value="C5001_PLUS">5000+</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="funding_stage">Funding Stage</Label>
                  <Select
                    value={watch('funding_stage')}
                    onValueChange={(value) => setValue('funding_stage', value as 'SEED' | 'PRE_SEED' | 'SERIES_A' | 'SERIES_B' | 'SERIES_C' | 'SERIES_D_PLUS' | 'PUBLIC')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select stage" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SEED">Seed</SelectItem>
                      <SelectItem value="PRE_SEED">Pre-Seed</SelectItem>
                      <SelectItem value="SERIES_A">Series A</SelectItem>
                      <SelectItem value="SERIES_B">Series B</SelectItem>
                      <SelectItem value="SERIES_C">Series C</SelectItem>
                      <SelectItem value="SERIES_D_PLUS">Series D+</SelectItem>
                      <SelectItem value="PUBLIC">Public</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="location_summary">Location Summary</Label>
                <Input
                  id="location_summary"
                  {...register('location_summary')}
                  placeholder="San Francisco, Remote, Global"
                />
              </div>
            </TabsContent>

            <TabsContent value="social" className="space-y-4">
              <div>
                <Label>Social Links</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  <div>
                    <Label htmlFor="social_twitter">Twitter Handle</Label>
                    <Input
                      id="social_twitter"
                      {...register('social_links.twitter')}
                      placeholder="@username"
                    />
                  </div>
                  <div>
                    <Label htmlFor="social_linkedin">LinkedIn</Label>
                    <Input
                      id="social_linkedin"
                      {...register('social_links.linkedin')}
                      placeholder="company/profile"
                    />
                  </div>
                  <div>
                    <Label htmlFor="social_github">GitHub</Label>
                    <Input
                      id="social_github"
                      {...register('social_links.github')}
                      placeholder="username or organization"
                    />
                  </div>
                  <div>
                    <Label htmlFor="social_discord">Discord</Label>
                    <Input
                      id="social_discord"
                      {...register('social_links.discord')}
                      placeholder="invite link or server"
                    />
                  </div>
                  <div>
                    <Label htmlFor="social_youtube">YouTube</Label>
                    <Input
                      id="social_youtube"
                      {...register('social_links.youtube')}
                      placeholder="channel name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="social_facebook">Facebook</Label>
                    <Input
                      id="social_facebook"
                      {...register('social_links.facebook')}
                      placeholder="page name"
                    />
                  </div>
                </div>
              </div>

              <div>
                <Label>Review & Sentiment Data</Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                  <div>
                    <Label htmlFor="scraped_review_count">Scraped Review Count</Label>
                    <Input
                      id="scraped_review_count"
                      type="number"
                      {...register('scraped_review_count', { valueAsNumber: true })}
                      placeholder="150"
                    />
                  </div>
                  <div>
                    <Label htmlFor="scraped_review_sentiment_score">Sentiment Score (0-1)</Label>
                    <Input
                      id="scraped_review_sentiment_score"
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      {...register('scraped_review_sentiment_score', { valueAsNumber: true })}
                      placeholder="0.85"
                    />
                  </div>
                  <div>
                    <Label htmlFor="scraped_review_sentiment_label">Sentiment Label</Label>
                    <Input
                      id="scraped_review_sentiment_label"
                      {...register('scraped_review_sentiment_label')}
                      placeholder="Positive"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="details" className="space-y-4">
              {/* Dynamic entity-specific details based on entity type */}
              {completeEntity?.entityType?.name === 'AI Tool' && (
                <div>
                  <Label className="text-lg font-semibold">Tool Details</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <Label htmlFor="tool_technical_level">Technical Level</Label>
                      <Select
                        value={watch('tool_details.technical_level')}
                        onValueChange={(value) => setValue('tool_details.technical_level', value as any)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select level" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="BEGINNER">Beginner</SelectItem>
                          <SelectItem value="INTERMEDIATE">Intermediate</SelectItem>
                          <SelectItem value="ADVANCED">Advanced</SelectItem>
                          <SelectItem value="EXPERT">Expert</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="tool_pricing_model">Pricing Model</Label>
                      <Input
                        id="tool_pricing_model"
                        {...register('tool_details.pricing_model')}
                        placeholder="e.g., Freemium, Subscription"
                      />
                    </div>

                    <div>
                      <Label htmlFor="tool_price_range">Price Range</Label>
                      <Select
                        value={watch('tool_details.price_range')}
                        onValueChange={(value) => setValue('tool_details.price_range', value as any)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select range" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="FREE">Free</SelectItem>
                          <SelectItem value="LOW">Low ($1-$50)</SelectItem>
                          <SelectItem value="MEDIUM">Medium ($51-$200)</SelectItem>
                          <SelectItem value="HIGH">High ($201-$1000)</SelectItem>
                          <SelectItem value="ENTERPRISE">Enterprise ($1000+)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="tool_api_documentation_url">API Documentation URL</Label>
                      <Input
                        id="tool_api_documentation_url"
                        {...register('tool_details.api_documentation_url')}
                        placeholder="https://docs.example.com/api"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="tool_has_free_tier"
                        {...register('tool_details.has_free_tier')}
                        className="rounded"
                      />
                      <Label htmlFor="tool_has_free_tier">Has Free Tier</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="tool_has_api"
                        {...register('tool_details.has_api')}
                        className="rounded"
                      />
                      <Label htmlFor="tool_has_api">Has API</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="tool_open_source"
                        {...register('tool_details.open_source')}
                        className="rounded"
                      />
                      <Label htmlFor="tool_open_source">Open Source</Label>
                    </div>
                  </div>

                  <div className="mt-4">
                    <Label htmlFor="tool_pricing_details">Pricing Details</Label>
                    <Textarea
                      id="tool_pricing_details"
                      {...register('tool_details.pricing_details')}
                      placeholder="Detailed pricing information..."
                      rows={3}
                    />
                  </div>
                </div>
              )}

              {completeEntity?.entityType?.name === 'Course' && (
                <div>
                  <Label className="text-lg font-semibold">Course Details</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <Label htmlFor="course_instructor_name">Instructor Name</Label>
                      <Input
                        id="course_instructor_name"
                        {...register('course_details.instructor_name')}
                        placeholder="Dr. Jane Smith"
                      />
                    </div>

                    <div>
                      <Label htmlFor="course_duration_text">Duration</Label>
                      <Input
                        id="course_duration_text"
                        {...register('course_details.duration_text')}
                        placeholder="8 weeks, 40 hours"
                      />
                    </div>

                    <div>
                      <Label htmlFor="course_skill_level">Skill Level</Label>
                      <Select
                        value={watch('course_details.skill_level')}
                        onValueChange={(value) => setValue('course_details.skill_level', value as any)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select level" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="BEGINNER">Beginner</SelectItem>
                          <SelectItem value="INTERMEDIATE">Intermediate</SelectItem>
                          <SelectItem value="ADVANCED">Advanced</SelectItem>
                          <SelectItem value="EXPERT">Expert</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="course_enrollment_count">Enrollment Count</Label>
                      <Input
                        id="course_enrollment_count"
                        type="number"
                        {...register('course_details.enrollment_count', { valueAsNumber: true })}
                        placeholder="1500"
                      />
                    </div>
                  </div>

                  <div className="mt-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="course_certificate_available"
                        {...register('course_details.certificate_available')}
                        className="rounded"
                      />
                      <Label htmlFor="course_certificate_available">Certificate Available</Label>
                    </div>
                  </div>

                  <div className="mt-4">
                    <Label htmlFor="course_prerequisites">Prerequisites</Label>
                    <Textarea
                      id="course_prerequisites"
                      {...register('course_details.prerequisites')}
                      placeholder="Basic programming knowledge, familiarity with Python..."
                      rows={3}
                    />
                  </div>
                </div>
              )}

              {completeEntity?.entityType?.name === 'Job' && (
                <div>
                  <Label className="text-lg font-semibold">Job Details</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <Label htmlFor="job_company_name">Company Name</Label>
                      <Input
                        id="job_company_name"
                        {...register('job_details.company_name')}
                        placeholder="Tech Corp Inc."
                      />
                    </div>

                    <div>
                      <Label htmlFor="job_experience_level">Experience Level</Label>
                      <Input
                        id="job_experience_level"
                        {...register('job_details.experience_level')}
                        placeholder="Senior, Mid, Entry"
                      />
                    </div>

                    <div>
                      <Label htmlFor="job_salary_min">Salary Min (in thousands)</Label>
                      <Input
                        id="job_salary_min"
                        type="number"
                        {...register('job_details.salary_min', { valueAsNumber: true })}
                        placeholder="80"
                      />
                    </div>

                    <div>
                      <Label htmlFor="job_salary_max">Salary Max (in thousands)</Label>
                      <Input
                        id="job_salary_max"
                        type="number"
                        {...register('job_details.salary_max', { valueAsNumber: true })}
                        placeholder="120"
                      />
                    </div>

                    <div>
                      <Label htmlFor="job_application_url">Application URL</Label>
                      <Input
                        id="job_application_url"
                        {...register('job_details.application_url')}
                        placeholder="https://company.com/jobs/apply"
                      />
                    </div>

                    <div>
                      <Label htmlFor="job_location">Location</Label>
                      <Input
                        id="job_location"
                        {...register('job_details.location')}
                        placeholder="San Francisco, CA"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="job_is_remote"
                        {...register('job_details.is_remote')}
                        className="rounded"
                      />
                      <Label htmlFor="job_is_remote">Remote Work Available</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="job_visa_sponsorship"
                        {...register('job_details.visa_sponsorship')}
                        className="rounded"
                      />
                      <Label htmlFor="job_visa_sponsorship">Visa Sponsorship</Label>
                    </div>
                  </div>

                  <div className="mt-4">
                    <Label htmlFor="job_description">Job Description</Label>
                    <Textarea
                      id="job_description"
                      {...register('job_details.job_description')}
                      placeholder="Detailed job description..."
                      rows={4}
                    />
                  </div>
                </div>
              )}

              {/* Show message for other entity types */}
              {!['AI Tool', 'Course', 'Job'].includes(completeEntity?.entityType?.name || '') && (
                <div className="text-center py-8 text-gray-500">
                  <p>Entity-specific details for "{completeEntity?.entityType?.name}" are not yet implemented.</p>
                  <p className="text-sm mt-2">This entity type supports additional fields that can be added in future updates.</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={watch('status')}
                  onValueChange={(value) => setValue('status', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PENDING">Pending</SelectItem>
                    <SelectItem value="ACTIVE">Active</SelectItem>
                    <SelectItem value="REJECTED">Rejected</SelectItem>
                    <SelectItem value="INACTIVE">Inactive</SelectItem>
                    <SelectItem value="ARCHIVED">Archived</SelectItem>
                    <SelectItem value="NEEDS_REVISION">Needs Revision</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="ref_link">Referral/Affiliate Link</Label>
                  <Input
                    id="ref_link"
                    {...register('ref_link')}
                    placeholder="https://example.com?ref=partner"
                  />
                </div>

                <div>
                  <Label htmlFor="affiliate_status">Affiliate Status</Label>
                  <Select
                    value={watch('affiliate_status')}
                    onValueChange={(value) => setValue('affiliate_status', value as 'NONE' | 'APPLIED' | 'APPROVED' | 'REJECTED')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="NONE">None</SelectItem>
                      <SelectItem value="APPLIED">Applied</SelectItem>
                      <SelectItem value="APPROVED">Approved</SelectItem>
                      <SelectItem value="REJECTED">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="privacy_policy_url">Privacy Policy URL</Label>
                <Input
                  id="privacy_policy_url"
                  {...register('privacy_policy_url')}
                  placeholder="https://example.com/privacy"
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="has_live_chat"
                  {...register('has_live_chat')}
                  className="rounded"
                />
                <Label htmlFor="has_live_chat">Has Live Chat Support</Label>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting || loading}
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || loading}
            >
              {(isSubmitting || loading) ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              Save Changes
            </Button>
          </DialogFooter>
        </form>
        )}
      </DialogContent>
    </Dialog>
  );
};
