import { Entity } from '@/types/entity';

/**
 * Generates a referral link from a website URL by adding ?ref=AIventory or &ref=AIventory
 * @param websiteUrl - The original website URL
 * @returns The website URL with AIventory referral parameter added
 */
export function generateReferralLink(websiteUrl: string): string {
  if (!websiteUrl) return '';
  
  try {
    const url = new URL(websiteUrl);
    // Check if URL already has query parameters
    if (url.search) {
      // Add &ref=AIventory if there are existing parameters
      url.searchParams.set('ref', 'AIventory');
    } else {
      // Add ?ref=AIventory if no existing parameters
      url.search = '?ref=AIventory';
    }
    return url.toString();
  } catch (error) {
    // If URL is invalid, just append the parameter
    const separator = websiteUrl.includes('?') ? '&' : '?';
    return `${websiteUrl}${separator}ref=AIventory`;
  }
}

/**
 * Gets the referral link for an entity, preferring the stored refLink but falling back to generated one
 * @param entity - The entity object
 * @returns The referral link to use for navigation
 */
export function getEntityReferralLink(entity: Entity): string {
  // If the entity has a stored refLink, use it
  if (entity.refLink) {
    return entity.refLink;
  }
  
  // Otherwise, generate one from the website URL
  return generateReferralLink(entity.websiteUrl);
}
